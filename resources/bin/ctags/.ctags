--langdef=vhdl
--langmap=vhdl:.vhd
--regex-vhdl=/^[ \t]*use[ \t]+([^ ]+);/\1/l,package/i
--regex-vhdl=/^[ \t]*entity[ \t]+([^ ]+) is/\1/e,entity/i
--regex-vhdl=/^[ \t]*signal[ \t]*([^:]+)/\1/s,signal/i
--regex-vhdl=/^[ \t]*type[ \t]*([^:]+)/\1/t,typedef/i
--regex-vhdl=/^[ \t]*alias[ \t]*([^:]+)/\1/a,alias/i
--regex-vhdl=/^[ \t]*constant[ \t]*([^:]+)/\1/c,constant/i
--regex-vhdl=/^[ \t]*variable[ \t]*([^:]+)/\1/v,variable/i
--regex-vhdl=/^[ \t]*([^ \t:]+)[ \t]*:[ \t]*process[ \t]*\(/\1/p,process/i
--regex-vhdl=/^[ \t]*([^ ]+)[ \t]*:[ \t]*(in|out|inout)/\1/o,port/i
--regex-vhdl=/^[ \t]*function[ \t]+([a-z0-9_]+)/\1/f,function/i

--langdef=tcl
--langmap=tcl:.tcl
--regex-tcl=/^[ \t]*(proc)[ \t]+([^ \t]+[ \t]+(\{.*\}|[^ \t]+))[ \t]+\{.*/\2/x,functions/
--regex-tcl=/^(set|variable)[ \t]+([^ \t]+)[ \t]+(.+)/\2/g,global,globals/
--regex-tcl=/^[ \t]*(namespace)[ \t]+[^ \t]+[ \t]+([^ \t]+)[ \t]+(.+)/\2/n,namespaces/
--regex-tcl=/^.+(set)[ \t]+([^ \t]+)[ \t]+(.+)/\2/v,variables,vars/
--regex-tcl=/^.*(array set)[ \t]+([^ \t]+)[ \t]+(.+)/\2/a,arrays/
--regex-tcl=/^[^\$]*(source)[ \t]+([^ \t]+)$/\2/s,sources/
--regex-tcl=/^.*(package require)[ \t]+([^ \t]+)$/\2/k,packages/
--regex-tcl=/^.*(st::)?addproc[ \t]+([^ \t]+)[ \t]+.*$/\2/u,tests/
--regex-tcl=/^.*interp[ \t]+alias[ \t]+[^ \t]+[ \t]+(.+).*$/\1/i,aliases/