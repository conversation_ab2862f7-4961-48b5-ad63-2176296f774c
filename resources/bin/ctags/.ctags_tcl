--langdef=tcl
--langmap=tcl:.tcl
--regex-tcl=/^[ \t]*(proc)[ \t]+([^ \t]+[ \t]+(\{.*\}|[^ \t]+))[ \t]+\{.*/\2/x,functions/
--regex-tcl=/^(set|variable)[ \t]+([^ \t]+)[ \t]+(.+)/\2/g,global,globals/
--regex-tcl=/^[ \t]*(namespace)[ \t]+[^ \t]+[ \t]+([^ \t]+)[ \t]+(.+)/\2/n,namespaces/
--regex-tcl=/^.+(set)[ \t]+([^ \t]+)[ \t]+(.+)/\2/v,variables,vars/
--regex-tcl=/^.*(array set)[ \t]+([^ \t]+)[ \t]+(.+)/\2/a,arrays/
--regex-tcl=/^[^\$]*(source)[ \t]+([^ \t]+)$/\2/s,sources/
--regex-tcl=/^.*(package require)[ \t]+([^ \t]+)$/\2/k,packages/
--regex-tcl=/^.*(st::)?addproc[ \t]+([^ \t]+)[ \t]+.*$/\2/u,tests/
--regex-tcl=/^.*interp[ \t]+alias[ \t]+[^ \t]+[ \t]+(.+).*$/\1/i,aliases/