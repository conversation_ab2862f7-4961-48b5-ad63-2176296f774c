// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: current file",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "sudo": false,
      "cwd": "${fileDirname}"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Jest Current File",
      "program": "${workspaceFolder}/node_modules/jest/bin/jest",
      "args": ["${fileBasenameNoExtension}"],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "test"
      },
      "runtimeArgs": ["--experimental-vm-modules", "--inspect-brk=0"],
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest"
      },
    },
    {
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}"
      ],
      "name": "Run TerosHDL",
      "sourceMaps": true,
      "outFiles": [
        "${workspaceFolder}/out/**/*.js",
      ],
      "preLaunchTask": "teros watch",
      "request": "launch",
      "type": "extensionHost"
    }
  ]
}
