- name: "build-package"
  command: "cd auto_package && ./gen.sh"

- name: "clean"
  command: "shx rm -rf out"

- name: "vscode:prepublish"
  command: "npm run generate-examples & npm run compile"

- name: "compile"
  command: "npm run-script copy-files & npm run-script build-webviews & npm run-script build-webviews-configuration & tsc -p ./"

- name: "lint"
  command: "eslint src"

- name: "watch"
  command: npm run-script copy-files & tsc --watch --p ./tsconfig.json & nodemon --ignore 'resources/webviews/reporters/**/wb/' --watch resources/webviews/reporters -e js,ts,tsx,html --exec \"npm run-script build-webviews\"

- name: "generate-examples"
  command: "cd ./resources/project_manager; ./copy_examples.sh"

- name: "pre-package"
  command: "mkdir ./node_modules/teroshdl2/node_modules/onml/lib; cp ./node_modules/teroshdl2/node_modules/onml/*.js ./node_modules/teroshdl2/node_modules/onml/lib"

- name: "package"
  command: "npm run pre-package & vsce package --allow-star-activation"

- name: "package-prerelease"
  command: "npm run pre-package & vsce package --pre-release -o ./pre-release.vsix --allow-star-activation"

- name: "auto-package"
  command: "cd auto_package & ./gen.sh"

- name : "build-webviews"
  command: "NODE_ENV=production node resources/webviews/reporters/esbuild.js"

- name : "build-webviews-configuration"
  command: "NODE_ENV=production node resources/webviews/fileConfiguration/esbuild.js"

- name : "compile-esbuild"
  command: "NODE_ENV=production node ./esbuild.js"

- name : "copy-files"
  command: "python3 copy_files.py "

- name : "test"
  command: "node --trace-warnings --experimental-vm-modules ./node_modules/.bin/jest --forceExit --runInBand --config ./jest.config.json"