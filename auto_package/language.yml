- id: "vhdl"
  extensions: [".vhd", ".vho", ".vhdl", ".vhd"]
  configuration: "./configs/vhdl.configuration.json"
  snippets: "./snippets/vhdl/vhdl.json"
  aliases: ["VHDL", "vhdl"]

- id: "verilog"
  extensions: [".v", ".vh", ".vl"]
  configuration: "./configs/verilog.configuration.json"
  snippets: "./snippets/verilog/verilog.json"
  aliases: ["Verilog", "verilog"]

- id: "systemverilog"
  extensions: [".sv", ".svh", ".SV"]
  configuration: "./configs/systemverilog.configuration.json"
  snippets: "./snippets/systemverilog/systemverilog.json"
  aliases: ["System Verilog", "systemverilog"]

- id: "vcd"
  extensions: [".vcd"]

- id: "gtkw_waveconfig"
  extensions: [".gtkw"]

- id: "gtkw_wavebin"
  extensions: [".fst", ".ghw"]

- id: "tcl"
  extensions: [".tcl", ".pro"]
  configuration: "./configs/tcl.configuration.json"
  snippets: "./snippets/tcl/tcl.json"
  aliases: ["TCL", "tcl"]

- id: "ucf"
  extensions: [".ucf"]
  configuration: "./configs/ucfconstraints.configuration.json"
  aliases: ["vivado ucf", "ucf constraints"]

- id: "xdc"
  extensions: [".xdc", ".sdc"]
  configuration: "./configs/xdcconstraints.configuration.json"
  snippets: "./snippets/xdc/xdc.json"
  aliases: ["vivado xdc", "xdc constraints"]

- id: "ldc"
  extensions: [".ldc", ".pdc"]
  configuration: "./configs/xdcconstraints.configuration.json"
  snippets: "./snippets/xdc/xdc.json"
  aliases: ["lattice constraints"]

- id: "TL-Verilog"
  extensions: [".tlv"]
  configuration: "./configs/tlverilog.configuration.json"
  snippets: "./snippets/tlverilog/tlverilog.json"
  aliases: ["TL-Verilog", "tlv", "Transactional-Level Verilog"]