################################################################################
# Others
################################################################################

- name: go_to_parent
  title: Go to parent file

- name: open
  title: Open file with TerosHDL

- name: configuration.global
  title: "TerosHDL: Open Global Settings Menu"

- name: configuration.verifySetup
  title: "TerosHDL: Verify Setup" 

################################################################################
# VSCode tree
################################################################################

- name: waveform
  title: "Open waveform viewer"
  where: ["explorer/context"]
  group: "navigation"
  when: "resourceLangId == gtkw_wavebin || resourceLangId == gtkw_waveconfig || resourceLangId == vcd"

################################################################################
# Project manager
################################################################################

## Project
- name: view.project.add
  title: "Add project"
  icon: add
  where: ["view/title"]
  when: "view == teroshdl-project"

- name: configuration.project
  title: "Open Project Settings"
  icon: gear
  where: ["view/item/context"]
  when: "viewItem == project && view == teroshdl-project"
  group: "inline"

- name: view.project.delete
  title: "Delete project"
  icon: chrome-close
  where: ["view/item/context"]
  when: "viewItem == project && view == teroshdl-project"
  group: "inline"

- name: view.project.rename
  title: "Rename project"
  icon: edit
  where: ["view/item/context"]
  when: "viewItem == project && view == teroshdl-project"
  group: "inline"

## Source
- name: view.source.save_project
  title: "Save Project"
  icon: save
  where: ["view/title"]
  when: "view == teroshdl-view-source"

- name: view.source.search
  title: "Search"
  icon: search
  where: ["view/title"]
  when: "view == teroshdl-view-source"

- name: view.source.add
  title: "Add"
  icon: add
  where: ["view/title"]
  when: "view == teroshdl-view-source"

- name: view.source.add_source_to_library
  title: "Add to library"
  icon: add
  where: ["view/item/context"]
  when: "viewItem == library && view == teroshdl-view-source"
  group: "inline"

- name: view.source.delete_library
  title: "Delete library"
  icon: dash
  where: ["view/item/context"]
  when: "viewItem == library && view == teroshdl-view-source"
  group: "inline"

- name: view.source.properties
  title: "File Properties"
  icon: gear
  where: ["view/item/context"]
  when: "viewItem == source && view == teroshdl-view-source"
  group: "inline"

- name: view.source.delete_source
  title: "Delete source"
  icon: dash
  where: ["view/item/context"]
  when: "viewItem == source && view == teroshdl-view-source"
  group: "inline"

- name: view.source.select_toplevel
  title: "Select source as toplevel"
  icon: check
  where: ["view/item/context"]
  when: "viewItem == source && view == teroshdl-view-source"
  group: "inline"

## Dependency
- name: view.dependency.refresh
  title: "Refresh"
  icon: refresh
  where: ["view/title"]
  when: "view == teroshdl-view-dependency"

- name: view.dependency.schematic
  title: "Schematic viewer"
  icon: list-tree
  where: ["view/title"]
  when: "view == teroshdl-view-dependency"

- name: view.dependency.viewer
  title: "Dependencies viewer"
  icon: dep
  where: ["view/title"]
  when: "view == teroshdl-view-dependency"

## Runs
- name: view.runs.run_all
  title: "Run all"
  icon: run-all
  where: ["view/title"]
  when: "view == teroshdl-view-runs"

- name: view.runs.stop
  title: "Stop"
  icon: stop
  where: ["view/title"]
  when: "view == teroshdl-view-runs"

- name: view.runs.refresh
  title: "Refresh"
  icon: refresh
  where: ["view/title"]
  when: "view == teroshdl-view-runs"

- name: view.runs.run
  title: "Run"
  icon: run
  where: ["view/item/context"]
  when: "viewItem == run && view == teroshdl-view-runs"
  group: "inline"

## Tasks
- name: view.tasks.stop
  title: "Stop"
  icon: stop
  where: ["view/title"]
  when: "view == teroshdl-view-tasks && view == teroshdl-view-tasks"

- name: view.tasks.clean
  title: "Clean project"
  icon: trash
  where: ["view/title"]
  when: "view == teroshdl-view-tasks && view == teroshdl-view-tasks"

- name: view.tasks.logs
  title: "Open Log"
  icon: database
  where: ["view/item/context"]
  when: "viewItem =~ /logs/ && view == teroshdl-view-tasks"
  group: "inline"

- name: view.tasks.report
  title: "Open Report"
  icon: output
  where: ["view/item/context"]
  when: "viewItem =~ /report/ && view == teroshdl-view-tasks"
  group: "inline"

- name: view.tasks.timing_analyzer
  title: "Open Timing Analyzer"
  icon: watch
  where: ["view/item/context"]
  when: "viewItem =~ /timinganalyzer/ && view == teroshdl-view-tasks"
  group: "inline"

- name: view.tasks.technology_map_viewer
  title: "Open Technology Map Viewer"
  icon: search
  where: ["view/item/context"]
  when: "viewItem =~ /technologymapviewer/ && view == teroshdl-view-tasks"
  group: "inline"

- name: view.tasks.snapshotviewer
  title: "Open Snapshop Viewer"
  icon: eye
  where: ["view/item/context"]
  when: "viewItem =~ /snapshotviewer/ && view == teroshdl-view-tasks"
  group: "inline"

- name: project.quartus.rtlAnalyzer
  title: "TerosHDL [Quartus]: Open RTL Analyzer"

- name: project.quartus.compileDesigh
  title: "TerosHDL [Quartus]: Run Compile Design"

- name: project.quartus.analysisAndSynthesis
  title: "TerosHDL [Quartus]: run Analysis and Synthesis"

- name: project.quartus.analysisAndElaboration
  title: "TerosHDL [Quartus]: run Analysis and Elaboration"

- name: project.quartus.synthesis
  title: "TerosHDL [Quartus]: run Synthesis"

- name: project.quartus.earlyTimingAnalysis
  title: "TerosHDL [Quartus]: run Early Timing Analysis"

- name: project.quartus.fitter
  title: "TerosHDL [Quartus]: run Fitter"

- name: project.quartus.fitterImplement
  title: "TerosHDL [Quartus]: run Fitter Implement"

- name: project.quartus.plan
  title: "TerosHDL [Quartus]: run Plan"

- name: project.quartus.place
  title: "TerosHDL [Quartus]: run Place"

- name: project.quartus.route
  title: "TerosHDL [Quartus]: run Route"

- name: project.quartus.fitterFinalize
  title: "TerosHDL [Quartus]: run Fitter Finalize"

- name: project.quartus.timingAnalysisSignoff
  title: "TerosHDL [Quartus]: run Timing Analysis Signoff"

- name: project.quartus.assembler
  title: "TerosHDL [Quartus]: run Assembler"

## Watcher
- name: view.watcher.add
  title: "Add"
  icon: add
  where: ["view/title"]
  when: "view == teroshdl-view-watcher && view == teroshdl-view-watcher"

- name: view.watcher.delete
  title: "Delete watcher"
  icon: dash
  where: ["view/item/context"]
  when: "viewItem == watcher && view == teroshdl-view-watcher"
  group: "inline"

## Outputs
- name: view.outputs.clean
  title: "Clean project"
  icon: trash
  where: ["view/title"]
  when: "view == teroshdl-view-output && view == teroshdl-view-output"

################################################################################
# Editor
################################################################################
- name: generate_template
  title: "TerosHDL: Generate template"
  icon: file-code
  where: ["editor/title"]

- name: state_machine.viewer
  title: State machine viewer
  icon: state-machine
  where: ["editor/title"]

- name: netlist.viewer
  title: Schematic viewer
  icon: list-tree
  where: ["editor/title"]

- name: format
  title: Format
  icon: check
  where: ["editor/title"]

- name: documentation.module
  title: Module documentation preview
  icon: book
  where: ["editor/title"]

# - name: quartus.add_ip
#   title: Add Quartus IP core
#   group: "TerosHDL"
#   where: ["editor/context"]
