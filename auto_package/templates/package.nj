{
{%- filter indent(width=4) %}
{% include 'info.nj' %}
{% endfilter %}
    "contributes": {
        "viewsContainers": {
            "activitybar": [
                {
                    "id": "teroshdl-project",
                    "title": "TerosHDL",
                    "icon": "resources/images/teros_logo.svg"
                }
            ],
            "panel": [
                {
                    "id": "teroshdl-messages-view",
                    "title": "TerosHDL: log report",
                    "icon": "fancy.icon"
                },
                {
                    "id": "teroshdl-timing-view",
                    "title": "TerosHDL: Timing",
                    "icon": "fancy.icon"
                }
            ]
        },
        "views": {
            "teroshdl-project": [
                {
                    "id": "teroshdl-project",
                    "name": "Projects"
                },
                {
                    "id": "teroshdl-view-source",
                    "name": "Files"
                },
                {
                    "id": "teroshdl-view-dependency",
                    "name": "Hierarchy"
                },
                {
                    "id": "teroshdl-view-watcher",
                    "name": "Watchers"
                },
                {
                    "id": "teroshdl-view-tasks",
                    "name": "Tasks"
                },
                {
                    "id": "teroshdl-view-runs",
                    "name": "Runs"
                },
                {
                    "id": "teroshdl-view-output",
                    "name": "Outputs"
                },
                {
                    "id": "teroshdl-view-ip-catalog",
                    "name": "IP Catalog"
                },
                {
                    "id": "teroshdl-view-actions",
                    "name": "Configuration"
                }
            ],
            "teroshdl-messages-view": [
                {
                    "type": "webview",
                    "id": "teroshdl-report-logs",
                    "name": "Report Logs"
                }
            ],
            "teroshdl-timing-view": [
                {
                    "type": "webview",
                    "id": "teroshdl-view-timing",
                    "name": "Timing Report"
                }
            ]
        },
        "viewsWelcome": [
            {
                "view": "teroshdl-project",
                "contents": "No TerosHDL project are open.\n[Add Project](command:teroshdl.view.project.add)\nTo learn more about how to [setup a TerosHDL project](https://terostechnology.github.io/terosHDLdoc/docs/project_configuration/)."
            },
            {
                "view": "teroshdl-view-source",
                "contents": "Learn more about how to [add files and libraries to a project](https://terostechnology.github.io/terosHDLdoc/docs/project_configuration/sources)."
            },
            {
                "view": "teroshdl-view-dependency",
                "contents": "Learn more about how to [setup TerosHDL to compute Hierarchy](https://terostechnology.github.io/terosHDLdoc/docs/project_configuration/hierarchy)."
            },
            {
                "view": "teroshdl-view-actions",
                "contents": "[Verify Setup](command:teroshdl.verifySetup)\n[Open Global Settings Menu](command:teroshdl.configuration.global)\n[Export Settings](command:teroshdl.view.project.export_configuration)\n[Load Settings](command:teroshdl.view.project.load_configuration)\n[Documentation](command:teroshdl.documentation)"
            }
      ],
{% filter indent(width=8) %}
{% include 'grammar.nj' %},
{% include 'menu.nj' %},
{% include 'command.nj' %},
{% include 'language_and_snippet.nj' %},
{% endfilter %}
        "keybindings": [
            {
                "command": "teroshdl.go_to_parent",
                "key": "alt+backspace",
                "mac": "command+delete",
                "when": "editorTextFocus"
            }
        ]
    },
{% filter indent(width=4) %}
{% include 'script.nj' %},
{% include 'dependencie.nj' %},
{% include 'devDependencie.nj' %}
{% endfilter %}
}
