"name": "teroshdl",
"displayName": "TerosHDL",
"publisher": "teros-technology",
"description": "Powerful toolbox for ASIC/FPGA: state machine viewer, linter, documentation, snippets... and more! ",
"version": "7.0.5",
"engines": {
    "vscode": "^1.74.0"
},
"keywords": [
    "tcl",
    "tk",
    "vhdl",
    "verilog",
    "systemverilog",
    "asic",
    "fpga",
    "raptor"
],
"categories": [
    "Programming Languages",
    "Snippets",
    "Formatters",
    "Linters"
],
"icon": "resources/images/icon_128_128_circle.png",
"homepage": "https://terostechnology.github.io/",
"repository": {
    "type": "git",
    "url": "https://github.com/TerosTechnology/vscode-terosHDL"
},
"bugs": {
    "url": "https://github.com/TerosTechnology/vscode-terosHDL/issues"
},
"main": "./out/teroshdl/extension.js",
"_moduleAliases": {
  "colibri": "./out/colibri"
},
