"menus": {
    "editor/context": [
{% set ns = namespace(counter = 0) %}
{%- for command in command_list %}
        {%- if "where" in command %}
            {%- for command_where in command["where"] %}
                {%- if command_where == "editor/context"%}
                    {%- if ns.counter == 0 %}
        {
                    {%- else %}
        ,{
                    {%- endif %}

                    {%- if "when" in command %}
            "when": "{{ command["when"] }}",
                    {%- else %}
            "when": "resourceLangId == verilog || resourceLangId == systemverilog || resourceLangId == vhdl",
                    {%- endif %}

            "command": "teroshdl.{{ command["name"] }}",
            "group": "navigation"
        }
                {% set ns.counter = ns.counter + 1 %}
                {%- endif %}
            {%- endfor %}
        {%- endif %}
{%- endfor %}
    ],
    "explorer/context": [
{% set ns = namespace(counter = 0) %}
{%- for command in command_list %}
        {%- if "where" in command %}
            {%- for command_where in command["where"] %}
                {%- if command_where == "explorer/context"%}
                    {%- if ns.counter == 0 %}
        {
                    {%- else %}
        ,{
                    {%- endif %}

                    {%- if "when" in command %}
            "when": "{{ command["when"] }}",
                    {%- else %}
            "when": "resourceLangId == verilog || resourceLangId == systemverilog || resourceLangId == vhdl",
                    {%- endif %}

            "command": "teroshdl.{{ command["name"] }}",
            "group": "navigation"
        }
                {% set ns.counter = ns.counter + 1 %}
                {%- endif %}
            {%- endfor %}
        {%- endif %}
{%- endfor %}
    ],
    "view/title": [
{% set ns = namespace(counter = 0) %}
{%- for command in command_list %}
        {%- if "where" in command %}
            {%- for command_where in command["where"] %}
                {%- if command_where == "view/title"%}
                    {%- if ns.counter == 0 %}
        {
                    {%- else %}
        ,{
                    {%- endif %}

                    {%- if "when" in command %}
            "when": "{{ command["when"] }}",
                    {%- else %}
            "when": "resourceLangId == verilog || resourceLangId == systemverilog || resourceLangId == vhdl",
                    {%- endif %}

            "command": "teroshdl.{{ command["name"] }}",
            "group": "navigation"
        }
                {% set ns.counter = ns.counter + 1 %}
                {%- endif %}
            {%- endfor %}
        {%- endif %}
{%- endfor %}
    ],
    "view/item/context": [
    {% set ns = namespace(counter = 0) %}
    {%- for command in command_list %}
            {%- if "where" in command %}
                {%- for command_where in command["where"] %}
                    {%- if command_where == "view/item/context"%}
                        {%- if ns.counter == 0 %}
            {
                        {%- else %}
            ,{
                        {%- endif %}
                "when": "{{ command["when"] }}",
                "command": "teroshdl.{{ command["name"] }}",
                "group": "{{ command["group"] }}"
            }
                    {% set ns.counter = ns.counter + 1 %}
                    {%- endif %}
                {%- endfor %}
            {%- endif %}
    {%- endfor %}
    ],
    "editor/title": [
{% set ns = namespace(counter = 0) %}
{%- for command in command_list %}
        {%- if "where" in command %}
            {%- for command_where in command["where"] %}
                {%- if command_where == "editor/title"%}
                    {%- if ns.counter == 0 %}
        {
                    {%- else %}
        ,{
                    {%- endif %}
            "when": "resourceLangId == verilog || resourceLangId == systemverilog || resourceLangId == vhdl",
            "command": "teroshdl.{{ command["name"] }}",
            "group": "navigation"
        }
                {% set ns.counter = ns.counter + 1 %}
                {%- endif %}
            {%- endfor %}
        {%- endif %}
{%- endfor %}
    ]
}