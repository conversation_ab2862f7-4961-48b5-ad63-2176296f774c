"dependencies": {
    "@octokit/rest": "16.36.0",
    "@types/node-fetch": "2.5.7",
    "@types/semver": "6.2.0",
    "abort-controller": "3.0.0",
    "fs-extra": "8.1.0",
    "netlistsvg": "https://github.com/TerosTechnology/netlistsvg.git#hierarchy",
    "open": "8.3.0",
    "proper-lockfile": "4.1.1",
    "semver": "7.3.5",
    "shelljs": "^0.8.3",
    "tmp": "0.2.1",
    "vscode-languageclient": "^9.0.1",
    "xml2js": "0.4.23",
    "nunjucks": "^3.2.0",
    "@types/nunjucks": "^3.1.4",
    "tree-kill": "1.2.2",
    "@vscode/webview-ui-toolkit": "^1.4.0",
    "esbuild-plugin-alias": "^0.2.1",
    "@oclif/core": "^1",
    "@oclif/plugin-help": "^5",
    "@oclif/plugin-plugins": "^2.0.1",
    "@yowasp/yosys": "0.41.721",
    "axios": "^1.7.2",
    "chokidar": "3.5.3",
    "cli-color": "^2.0.3",
    "clone": "^2.1.2",
    "console-table-printer": "^2.11.0",
    "fast-xml-parser": "4.3.2",
    "jest-html-reporter": "^3.7.0",
    "js-yaml": "^4.1.0",
    "json5": "2.1.3",
    "lodash": "^4.17.21",
    "onml": "2.1.0",
    "pyodide": "0.24.1",
    "showdown": "1.9.1",
    "showdown-highlight": "2.1.8",
    "sql.js": "^1.9.0",
    "state-machine-cat": "10.1.11",
    "fast-json-stable-stringify": "^2.1.0",
    "svg.js": "2.7.1",
    "svgdom": "^0.0.21",
    "temp": "0.9.4",
    "tiny-glob": "^0.2.9",
    "wavedrom": "3.3.0",
    "web-tree-sitter": "^0.20.7",
    "module-alias": "^2.2.3"
}