{"name": "teroshdl-lite", "displayName": "TerosHDL Lite", "publisher": "teros-technology", "description": "Lightweight syntax highlighting for HDL languages (VHDL, Verilog, SystemVerilog, TCL, XDC, UCF, TL-Verilog)", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "keywords": ["tcl", "tk", "vhdl", "verilog", "systemverilog", "asic", "fpga", "syntax", "highlighting"], "categories": ["Programming Languages", "Snippets"], "icon": "resources/images/icon_128_128_circle.png", "homepage": "https://terostechnology.github.io/", "repository": {"type": "git", "url": "https://github.com/TerosTechnology/vscode-terosHDL"}, "bugs": {"url": "https://github.com/TerosTechnology/vscode-terosHDL/issues"}, "main": "./out/extension.js", "contributes": {"grammars": [{"language": "vhdl", "scopeName": "source.vhdl", "path": "./syntaxes/vhdl/vhdl.tmLanguage.json"}, {"language": "verilog", "scopeName": "source.verilog", "path": "./syntaxes/verilog.tmLanguage"}, {"language": "systemverilog", "scopeName": "source.systemverilog", "path": "./syntaxes/systemverilog.tmLanguage"}, {"language": "tcl", "scopeName": "source.tcl", "path": "./syntaxes/tcl/tcl.tmLanguage.json"}, {"language": "xdc", "scopeName": "source.xdcconstraints", "path": "./syntaxes/xdc.tmLanguage"}, {"language": "ldc", "scopeName": "source.xdcconstraints", "path": "./syntaxes/xdc.tmLanguage"}, {"language": "ucf", "scopeName": "source.ucfconstraints", "path": "./syntaxes/ucf.tmLanguage"}, {"language": "TL-Verilog", "scopeName": "source.tlverilog", "path": "./syntaxes/tlverilog.tmLanguage"}], "languages": [{"id": "vhdl", "aliases": ["VHDL", "vhdl"], "configuration": "./configs/vhdl.configuration.json", "extensions": [".vhd", ".vho", ".vhdl", ".vhd"]}, {"id": "verilog", "aliases": ["Verilog", "verilog"], "configuration": "./configs/verilog.configuration.json", "extensions": [".v", ".vh", ".vl"]}, {"id": "systemverilog", "aliases": ["System Verilog", "systemverilog"], "configuration": "./configs/systemverilog.configuration.json", "extensions": [".sv", ".svh", ".SV"]}, {"id": "tcl", "aliases": ["TCL", "tcl"], "configuration": "./configs/tcl.configuration.json", "extensions": [".tcl", ".pro"]}, {"id": "ucf", "aliases": ["vivado ucf", "ucf constraints"], "configuration": "./configs/ucfconstraints.configuration.json", "extensions": [".ucf"]}, {"id": "xdc", "aliases": ["vivado xdc", "xdc constraints"], "configuration": "./configs/xdcconstraints.configuration.json", "extensions": [".xdc", ".sdc"]}, {"id": "ldc", "aliases": ["lattice constraints"], "configuration": "./configs/xdcconstraints.configuration.json", "extensions": [".ldc", ".pdc"]}, {"id": "TL-Verilog", "aliases": ["TL-Verilog", "tlv", "Transactional-Level Verilog"], "configuration": "./configs/tlverilog.configuration.json", "extensions": [".tlv"]}], "snippets": [{"language": "vhdl", "path": "./snippets/vhdl/vhdl.json"}, {"language": "verilog", "path": "./snippets/verilog/verilog.json"}, {"language": "systemverilog", "path": "./snippets/systemverilog/systemverilog.json"}, {"language": "tcl", "path": "./snippets/tcl/tcl.json"}, {"language": "xdc", "path": "./snippets/xdc/xdc.json"}, {"language": "ldc", "path": "./snippets/xdc/xdc.json"}, {"language": "TL-Verilog", "path": "./snippets/tlverilog/tlverilog.json"}, {"language": "systemverilog", "path": "./snippets/verilog/verilog.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package"}, "devDependencies": {"@types/node": "^17.0.31", "@types/vscode": "^1.45.0", "typescript": "^4.8.2"}}