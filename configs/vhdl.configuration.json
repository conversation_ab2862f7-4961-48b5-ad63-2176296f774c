// Copyright (c) 2019 <PERSON>

{
    "comments": {
        "lineComment": "--",
        "blockComment": [ "/*", "*/" ]
    },
    "brackets": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
    ],
    "autoClosingPairs": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"],
        ["\"", "\""],
        ["'", "'"]
    ],
    "autoCloseBefore": ";:,=])\n\t",
    "surroundingPairs": [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"],
        ["\"", "\""],
        ["'", "'"]
    ],
    "wordPattern": "(?<!\\\\)\\b([0-9a-zA-Z]+[0-9a-zA-Z_]*)\\b(?!\\\\)|(\\\\([0-9a-zA-Z]+[0-9a-zA-Z_\\\\]*)\\\\)",
    "indentationRules": {
        "increaseIndentPattern": "^((?!(--|/\\*)).)*(\\b(begin|else|loop|generate|then)\\b|\\b(component|block|process|is|generic|port)\\b[^;]*$)",
        // `^((?!(--|/\*)).)*` ignore any text in a comment
        // `\b(begin|else|loop|generate|then)\b` increase indent on a line containing these keywords
        // `\b(component|block|process|is|generic|port)\b[^;]*$` only increase indent a line containing these keywords if no `;` follows on the same line.
        "decreaseIndentPattern": "^((?!.*?\\/\\*).*\\*/)?\\s*(elsif|else|end|begin)\\b",
        // `^((?!.*?/\*).*\*/)??` ignore any text in a comment at the start of the line
        // `\s*(elsif|else|end|begin)\b)` decrease indent on a line starting with these keywords
    },

    "folding": { // enable folding/minimap labels for --#region <Label> ... --#endregion <Label>
        "markers": {
            "start": "^\\s*\\-\\-\\s*#?region\\b",
            "end": "^\\s*\\-\\-\\s*#?endregion\\b"
        }
    }
}
